import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_application_1/components/route_card.dart';
import 'package:flutter_application_1/components/wide_button.dart';
import 'package:shimmer/shimmer.dart';

class RouteSearch extends StatefulWidget {
  const RouteSearch({Key? key}) : super(key: key);

  @override
  State<RouteSearch> createState() => _RouteSearchState();
}

class _RouteSearchState extends State<RouteSearch> {
  final _fromController = TextEditingController();
  final _toController = TextEditingController();
  final db = FirebaseFirestore.instance;
  bool _isSearching = false;
  String error = '';
  List<DocumentReference> results = [];
  void _search() async {
    setState(() {
      _isSearching = true;
      error = '';
      results = [];
    });

    try {
      // Validate input fields
      if (_fromController.text.trim().isEmpty ||
          _toController.text.trim().isEmpty) {
        setState(() {
          error = 'Ambele câmpuri sunt obligatorii';
          _isSearching = false;
        });
        return;
      }

      final fromText = _fromController.text.trim().toLowerCase();
      final toText = _toController.text.trim().toLowerCase();

      if (fromText == toText) {
        setState(() {
          error = 'Staţiile trebuie să fie diferite';
          _isSearching = false;
        });
        return;
      }

      // Search for stops in parallel for better performance
      final futures = await Future.wait([
        db.collection('stops').where("name", isEqualTo: fromText).get(),
        db.collection('stops').where("name", isEqualTo: toText).get(),
      ]);

      final from = futures[0];
      final to = futures[1];

      if (from.docs.isEmpty) {
        setState(() {
          _isSearching = false;
          error = 'Staţia de pornire "$fromText" nu a fost găsită';
        });
        return;
      }

      if (to.docs.isEmpty) {
        setState(() {
          _isSearching = false;
          error = 'Staţia de sosire "$toText" nu a fost găsită';
        });
        return;
      }

      final fromRef = from.docs[0].reference;
      final toRef = to.docs[0].reference;

      // Search for routes containing the starting stop
      final routeResult = await db
          .collection('routes')
          .where('stops', arrayContains: fromRef)
          .get();

      final tempResult = <DocumentReference>[];
      for (final route in routeResult.docs) {
        final routeData = route.data();
        if (routeData['stops'] != null) {
          final stops = routeData['stops'] as List<dynamic>;
          final fromIndex = stops.indexOf(fromRef);
          final toIndex = stops.indexOf(toRef);

          // Check if destination stop exists and comes after starting stop
          if (toIndex != -1 && fromIndex < toIndex) {
            tempResult.add(route.reference);
          }
        }
      }

      if (tempResult.isEmpty) {
        setState(() {
          _isSearching = false;
          error = 'Nu există rute directe între aceste staţii';
        });
        return;
      }

      setState(() {
        results = tempResult;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
        error = 'Eroare la căutarea rutelor: ${e.toString()}';
      });
    }
  }

  @override
  void dispose() {
    _fromController.dispose();
    _toController.dispose();
    super.dispose();
  }

  void _clearSearch() {
    setState(() {
      _fromController.clear();
      _toController.clear();
      results.clear();
      error = '';
    });
  }

  void _swapStations() {
    final temp = _fromController.text;
    _fromController.text = _toController.text;
    _toController.text = temp;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 40),
              child: Column(
                children: [
                  Icon(
                    Icons.directions_bus,
                    size: 48,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "Căutați rute între stațiile de autobuz",
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // Search Form
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // From field
                    TextField(
                      controller: _fromController,
                      textCapitalization: TextCapitalization.words,
                      decoration: const InputDecoration(
                        label: Text("Punctul de pornire"),
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.my_location),
                        hintText: "Introduceți numele stației de pornire",
                      ),
                    ),

                    // Swap button
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: IconButton(
                        onPressed: _swapStations,
                        icon: const Icon(Icons.swap_vert),
                        tooltip: "Schimbă stațiile",
                        style: IconButton.styleFrom(
                          backgroundColor: Theme.of(
                            context,
                          ).primaryColor.withValues(alpha: 0.1),
                        ),
                      ),
                    ),

                    // To field
                    TextField(
                      controller: _toController,
                      textCapitalization: TextCapitalization.words,
                      decoration: const InputDecoration(
                        label: Text("Punct de sosire"),
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_on),
                        hintText: "Introduceți numele stației de sosire",
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: _isSearching
                              ? Shimmer.fromColors(
                                  baseColor: Colors.grey.shade300,
                                  highlightColor: Colors.grey.shade100,
                                  child: Container(
                                    height: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade300,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                )
                              : WideButton(
                                  callback: _search,
                                  text: "Găsește rute",
                                ),
                        ),
                        if (results.isNotEmpty || error.isNotEmpty) ...[
                          const SizedBox(width: 12),
                          IconButton(
                            onPressed: _clearSearch,
                            icon: const Icon(Icons.clear),
                            tooltip: "Șterge căutarea",
                            style: IconButton.styleFrom(
                              backgroundColor: Colors.grey.shade200,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Error message
            if (error.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Card(
                  color: Colors.red.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(Icons.error_outline, color: Colors.red.shade700),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            error,
                            style: TextStyle(
                              color: Colors.red.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // Results
            if (results.isNotEmpty) ...[
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Text(
                  "Rute găsite (${results.length})",
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: results.length,
                itemBuilder: (context, index) {
                  return RouteCard(
                    route: results[index],
                    key: Key(results[index].id),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }
}
