import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_application_1/components/wide_button.dart';
import 'package:shimmer/shimmer.dart';

class DemoRouteSearch extends StatefulWidget {
  const DemoRouteSearch({Key? key}) : super(key: key);

  @override
  State<DemoRouteSearch> createState() => _DemoRouteSearchState();
}

class _DemoRouteSearchState extends State<DemoRouteSearch> {
  final _fromController = TextEditingController();
  final _toController = TextEditingController();
  bool _isSearching = false;
  String error = '';
  List<String> results = [];
  
  // New features
  List<String> _recentSearches = [];
  List<String> _favoriteStops = [];
  List<String> _allStops = [
    'bến xe miền đông',
    'bến xe miền tây', 
    'sân bay tân sơn nhất',
    'chợ bến thành',
    'nhà thờ đức bà',
    'dinh độc lập',
    'công viên tao đàn',
    'trung tâm thương mại diamond plaza',
    'bệnh viện chợ rẫy',
    'đại học bách khoa',
    'khu công nghệ cao',
    'cầu sài gòn',
    'cầu thủ thiêm',
    'quận 1',
    'quận 3', 
    'quận 7',
    'thủ đức',
    'bình thạnh',
    'gò vấp',
    'tân bình'
  ];
  String _selectedTimeFilter = 'all';
  bool _onlyActiveRoutes = true;
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    // Demo data
    _favoriteStops = ['bến xe miền đông', 'sân bay tân sơn nhất', 'chợ bến thành'];
    _recentSearches = [
      'bến xe miền đông → sân bay tân sơn nhất',
      'chợ bến thành → dinh độc lập',
    ];
  }

  void _search() async {
    setState(() {
      _isSearching = true;
      error = '';
      results = [];
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    try {
      // Validate input fields
      if (_fromController.text.trim().isEmpty ||
          _toController.text.trim().isEmpty) {
        setState(() {
          error = 'Ambele câmpuri sunt obligatorii';
          _isSearching = false;
        });
        return;
      }

      final fromText = _fromController.text.trim().toLowerCase();
      final toText = _toController.text.trim().toLowerCase();

      if (fromText == toText) {
        setState(() {
          error = 'Staţiile trebuie să fie diferite';
          _isSearching = false;
        });
        return;
      }

      // Demo results
      final demoResults = [
        'Tuyến 01: $fromText → $toText (30 phút)',
        'Tuyến 15: $fromText → $toText (45 phút)', 
        'Tuyến 23: $fromText → $toText (25 phút)',
      ];

      // Add to recent searches
      _addToRecentSearches(fromText, toText);
      
      setState(() {
        results = demoResults;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
        error = 'Eroare la căutarea rutelor: ${e.toString()}';
      });
    }
  }

  @override
  void dispose() {
    _fromController.dispose();
    _toController.dispose();
    super.dispose();
  }

  void _clearSearch() {
    setState(() {
      _fromController.clear();
      _toController.clear();
      results.clear();
      error = '';
    });
  }

  void _swapStations() {
    final temp = _fromController.text;
    _fromController.text = _toController.text;
    _toController.text = temp;
  }

  // Add to recent searches
  void _addToRecentSearches(String from, String to) {
    final searchQuery = '$from → $to';
    setState(() {
      _recentSearches.remove(searchQuery);
      _recentSearches.insert(0, searchQuery);
      if (_recentSearches.length > 5) {
        _recentSearches = _recentSearches.take(5).toList();
      }
    });
  }

  // Toggle favorite stop
  void _toggleFavoriteStop(String stopName) {
    setState(() {
      if (_favoriteStops.contains(stopName)) {
        _favoriteStops.remove(stopName);
      } else {
        _favoriteStops.add(stopName);
      }
    });
  }

  // Share route
  void _shareRoute(String from, String to) {
    final text = 'Tuyến đường từ $from đến $to - Tìm kiếm bằng ứng dụng Bus Route';
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Đã sao chép thông tin tuyến đường!'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Toggle theme
  void _toggleTheme() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
  }

  // Show filters bottom sheet
  void _showFiltersBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Bộ lọc tìm kiếm',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              
              // Time filter
              Text('Thời gian hoạt động:', 
                style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: [
                  FilterChip(
                    label: const Text('Tất cả'),
                    selected: _selectedTimeFilter == 'all',
                    onSelected: (selected) {
                      setModalState(() => _selectedTimeFilter = 'all');
                      setState(() => _selectedTimeFilter = 'all');
                    },
                  ),
                  FilterChip(
                    label: const Text('Sáng (6-12h)'),
                    selected: _selectedTimeFilter == 'morning',
                    onSelected: (selected) {
                      setModalState(() => _selectedTimeFilter = 'morning');
                      setState(() => _selectedTimeFilter = 'morning');
                    },
                  ),
                  FilterChip(
                    label: const Text('Chiều (12-18h)'),
                    selected: _selectedTimeFilter == 'afternoon',
                    onSelected: (selected) {
                      setModalState(() => _selectedTimeFilter = 'afternoon');
                      setState(() => _selectedTimeFilter = 'afternoon');
                    },
                  ),
                  FilterChip(
                    label: const Text('Tối (18-22h)'),
                    selected: _selectedTimeFilter == 'evening',
                    onSelected: (selected) {
                      setModalState(() => _selectedTimeFilter = 'evening');
                      setState(() => _selectedTimeFilter = 'evening');
                    },
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Active routes only
              SwitchListTile(
                title: const Text('Chỉ hiển thị tuyến đang hoạt động'),
                value: _onlyActiveRoutes,
                onChanged: (value) {
                  setModalState(() => _onlyActiveRoutes = value);
                  setState(() => _onlyActiveRoutes = value);
                },
              ),
              
              const SizedBox(height: 20),
              
              // Apply button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _search(); // Re-search with new filters
                  },
                  child: const Text('Áp dụng bộ lọc'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: _isDarkMode ? ThemeData.dark(useMaterial3: true) : ThemeData.light(useMaterial3: true),
      child: Scaffold(
        backgroundColor: _isDarkMode ? Colors.grey[900] : Colors.grey[50],
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header with actions
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 40),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          IconButton(
                            onPressed: _toggleTheme,
                            icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
                            tooltip: _isDarkMode ? 'Chế độ sáng' : 'Chế độ tối',
                          ),
                          Column(
                            children: [
                              Icon(
                                Icons.directions_bus,
                                size: 48,
                                color: Theme.of(context).primaryColor,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                "Tìm kiếm tuyến xe bus",
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                          IconButton(
                            onPressed: _showFiltersBottomSheet,
                            icon: const Icon(Icons.tune),
                            tooltip: 'Bộ lọc',
                          ),
                        ],
                      ),
                      if (_recentSearches.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Tìm kiếm gần đây:',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        SizedBox(
                          height: 40,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _recentSearches.length,
                            itemBuilder: (context, index) {
                              final search = _recentSearches[index];
                              final parts = search.split(' → ');
                              return Padding(
                                padding: const EdgeInsets.only(right: 8),
                                child: ActionChip(
                                  label: Text(search),
                                  onPressed: () {
                                    if (parts.length == 2) {
                                      _fromController.text = parts[0];
                                      _toController.text = parts[1];
                                    }
                                  },
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Search Form
                Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        // From field with autocomplete
                        Autocomplete<String>(
                          optionsBuilder: (textEditingValue) {
                            if (textEditingValue.text.isEmpty) {
                              return _favoriteStops;
                            }
                            return _allStops.where((stop) => stop
                                .toLowerCase()
                                .contains(textEditingValue.text.toLowerCase()));
                          },
                          onSelected: (selection) {
                            _fromController.text = selection;
                          },
                          fieldViewBuilder: (context, controller, focusNode, onEditingComplete) {
                            _fromController.text = controller.text;
                            return TextField(
                              controller: controller,
                              focusNode: focusNode,
                              onEditingComplete: onEditingComplete,
                              textCapitalization: TextCapitalization.words,
                              decoration: InputDecoration(
                                label: const Text("Punctul de pornire"),
                                border: const OutlineInputBorder(),
                                prefixIcon: const Icon(Icons.my_location),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _favoriteStops.contains(_fromController.text.toLowerCase())
                                        ? Icons.favorite
                                        : Icons.favorite_border,
                                    color: _favoriteStops.contains(_fromController.text.toLowerCase())
                                        ? Colors.red
                                        : null,
                                  ),
                                  onPressed: () => _toggleFavoriteStop(_fromController.text.toLowerCase()),
                                  tooltip: 'Thêm vào yêu thích',
                                ),
                                hintText: "Introduceți numele stației de pornire",
                              ),
                            );
                          },
                        ),

                        // Swap button
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: IconButton(
                            onPressed: _swapStations,
                            icon: const Icon(Icons.swap_vert),
                            tooltip: "Schimbă stațiile",
                            style: IconButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                            ),
                          ),
                        ),

                        // To field with autocomplete
                        Autocomplete<String>(
                          optionsBuilder: (textEditingValue) {
                            if (textEditingValue.text.isEmpty) {
                              return _favoriteStops;
                            }
                            return _allStops.where((stop) => stop
                                .toLowerCase()
                                .contains(textEditingValue.text.toLowerCase()));
                          },
                          onSelected: (selection) {
                            _toController.text = selection;
                          },
                          fieldViewBuilder: (context, controller, focusNode, onEditingComplete) {
                            _toController.text = controller.text;
                            return TextField(
                              controller: controller,
                              focusNode: focusNode,
                              onEditingComplete: onEditingComplete,
                              textCapitalization: TextCapitalization.words,
                              decoration: InputDecoration(
                                label: const Text("Punct de sosire"),
                                border: const OutlineInputBorder(),
                                prefixIcon: const Icon(Icons.location_on),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _favoriteStops.contains(_toController.text.toLowerCase())
                                        ? Icons.favorite
                                        : Icons.favorite_border,
                                    color: _favoriteStops.contains(_toController.text.toLowerCase())
                                        ? Colors.red
                                        : null,
                                  ),
                                  onPressed: () => _toggleFavoriteStop(_toController.text.toLowerCase()),
                                  tooltip: 'Thêm vào yêu thích',
                                ),
                                hintText: "Introduceți numele stației de sosire",
                              ),
                            );
                          },
                        ),

                        const SizedBox(height: 20),

                        // Action buttons
                        Row(
                          children: [
                            Expanded(
                              child: _isSearching
                                  ? Shimmer.fromColors(
                                      baseColor: Colors.grey.shade300,
                                      highlightColor: Colors.grey.shade100,
                                      child: Container(
                                        height: 60,
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade300,
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                      ),
                                    )
                                  : WideButton(
                                      callback: _search,
                                      text: "Găsește rute",
                                    ),
                            ),
                            if (results.isNotEmpty || error.isNotEmpty) ...[
                              const SizedBox(width: 12),
                              IconButton(
                                onPressed: _clearSearch,
                                icon: const Icon(Icons.clear),
                                tooltip: "Șterge căutarea",
                                style: IconButton.styleFrom(
                                  backgroundColor: Colors.grey.shade200,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // Error message
                if (error.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Card(
                      color: Colors.red.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Icon(Icons.error_outline, color: Colors.red.shade700),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                error,
                                style: TextStyle(
                                  color: Colors.red.shade700,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                // Results
                if (results.isNotEmpty) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Rute găsite (${results.length})",
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          onPressed: () => _shareRoute(
                            _fromController.text,
                            _toController.text,
                          ),
                          icon: const Icon(Icons.share),
                          tooltip: 'Chia sẻ tuyến đường',
                          style: IconButton.styleFrom(
                            backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                          ),
                        ),
                      ],
                    ),
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: results.length,
                    itemBuilder: (context, index) {
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: const Icon(Icons.directions_bus),
                          title: Text(results[index]),
                          subtitle: Text('Bộ lọc: $_selectedTimeFilter | Chỉ tuyến hoạt động: ${_onlyActiveRoutes ? "Có" : "Không"}'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                        ),
                      );
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
